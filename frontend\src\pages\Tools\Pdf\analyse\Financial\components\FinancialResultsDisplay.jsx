// src/pages/Tools/Pdf/analyse/Financial/components/FinancialResultsDisplay.jsx
import React, { useState } from 'react';
import { FiDollarSign, FiTrendingUp, FiBarChart3, FiPieChart, FiDownload, FiEye, FiChevronDown, FiChevronUp } from 'react-icons/fi';

const FinancialResultsDisplay = ({ results }) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [expandedSections, setExpandedSections] = useState({
    tables: true,
    metrics: true,
    ratios: true,
    analysis: true
  });

  const toggleSection = (section) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  const formatCurrency = (value) => {
    if (!value) return 'N/A';
    // Simple currency formatting - can be enhanced
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const renderKeyMetrics = () => {
    const aiAnalysis = results.data?.analysis_results?.ai_analysis;
    const keyMetrics = aiAnalysis?.key_metrics || results.data?.analysis_results?.key_metrics || {};

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {Object.entries(keyMetrics).map(([key, value]) => (
          <div key={key} className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 rounded-lg border border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600 capitalize">
                  {key.replace(/_/g, ' ')}
                </p>
                <p className="text-lg font-bold text-blue-900">
                  {formatCurrency(value)}
                </p>
              </div>
              <FiDollarSign className="text-blue-500 w-6 h-6" />
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderFinancialRatios = () => {
    const aiAnalysis = results.data?.analysis_results?.ai_analysis;
    const ratios = aiAnalysis?.financial_ratios || {};

    if (Object.keys(ratios).length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FiBarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No financial ratios found in the analysis</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {Object.entries(ratios).map(([category, categoryRatios]) => (
          <div key={category} className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-lg font-semibold text-gray-800 mb-3 capitalize">
              {category.replace(/_/g, ' ')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {typeof categoryRatios === 'object' && categoryRatios !== null ? 
                Object.entries(categoryRatios).map(([ratio, value]) => (
                  <div key={ratio} className="bg-white p-3 rounded border">
                    <p className="text-sm text-gray-600 capitalize">
                      {ratio.replace(/_/g, ' ')}
                    </p>
                    <p className="text-lg font-semibold text-gray-900">
                      {value || 'N/A'}
                    </p>
                  </div>
                )) :
                <div className="bg-white p-3 rounded border">
                  <p className="text-sm text-gray-600">Value</p>
                  <p className="text-lg font-semibold text-gray-900">
                    {categoryRatios || 'N/A'}
                  </p>
                </div>
              }
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderTables = () => {
    const tables = results.data?.analysis_results?.tables || [];
    const aiTables = results.data?.analysis_results?.ai_analysis?.financial_tables || [];
    
    const allTables = [...tables, ...aiTables];

    if (allTables.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FiPieChart className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No financial tables found in the document</p>
        </div>
      );
    }

    return (
      <div className="space-y-6">
        {allTables.map((table, index) => (
          <div key={index} className="bg-white border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
              <h4 className="text-lg font-semibold text-gray-800">
                {table.table_type || `Table ${index + 1}`}
              </h4>
            </div>
            <div className="p-4">
              {table.table_data ? (
                <div className="overflow-x-auto">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {JSON.stringify(table.table_data, null, 2)}
                  </pre>
                </div>
              ) : (
                <div className="bg-gray-50 p-4 rounded">
                  <pre className="text-sm text-gray-700 whitespace-pre-wrap">
                    {table.table_text || 'No table data available'}
                  </pre>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  const renderAnalysisSummary = () => {
    const aiAnalysis = results.data?.analysis_results?.ai_analysis;
    const summary = aiAnalysis?.summary || aiAnalysis?.raw_analysis;

    if (!summary) {
      return (
        <div className="text-center py-8 text-gray-500">
          <FiEye className="w-12 h-12 mx-auto mb-4 opacity-50" />
          <p>No analysis summary available</p>
        </div>
      );
    }

    return (
      <div className="bg-gradient-to-br from-green-50 to-green-100 p-6 rounded-lg border border-green-200">
        <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center">
          <FiTrendingUp className="mr-2" />
          Financial Analysis Summary
        </h4>
        <div className="prose prose-green max-w-none">
          <p className="text-green-700 leading-relaxed whitespace-pre-wrap">
            {summary}
          </p>
        </div>
      </div>
    );
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiBarChart3 },
    { id: 'metrics', label: 'Key Metrics', icon: FiDollarSign },
    { id: 'ratios', label: 'Financial Ratios', icon: FiTrendingUp },
    { id: 'tables', label: 'Tables', icon: FiPieChart },
  ];

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold mb-2">Financial Analysis Results</h2>
            <p className="text-blue-100">
              Document: {results.originalFilename || 'Financial Document'}
            </p>
            <p className="text-blue-100 text-sm">
              Analysis Type: {results.analysisType || 'Comprehensive'}
            </p>
          </div>
          <div className="flex space-x-2">
            <button className="bg-blue-500 hover:bg-blue-400 px-4 py-2 rounded-lg flex items-center transition-colors">
              <FiDownload className="mr-2" />
              Export
            </button>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8 px-6">
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center py-4 px-2 border-b-2 font-medium text-sm transition-colors
                  ${activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <IconComponent className="mr-2 w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Content */}
      <div className="p-6">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800">Key Metrics</h3>
                  <button
                    onClick={() => toggleSection('metrics')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    {expandedSections.metrics ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                </div>
                {expandedSections.metrics && renderKeyMetrics()}
              </div>
              <div>
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-800">Analysis Summary</h3>
                  <button
                    onClick={() => toggleSection('analysis')}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    {expandedSections.analysis ? <FiChevronUp /> : <FiChevronDown />}
                  </button>
                </div>
                {expandedSections.analysis && renderAnalysisSummary()}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'metrics' && renderKeyMetrics()}
        {activeTab === 'ratios' && renderFinancialRatios()}
        {activeTab === 'tables' && renderTables()}
      </div>
    </div>
  );
};

export default FinancialResultsDisplay;
