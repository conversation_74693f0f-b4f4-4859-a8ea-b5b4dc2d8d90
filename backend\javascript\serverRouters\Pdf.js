// serverRouters/Pdf.js
import express from 'express';
import pdfChatRoutes from '../routes/Tools/pdf/chatRoutes.js';
import pdfApiRoutes from '../routes/Tools/pdf/SummurazeRouter.js';
import mindmapInternalRoutes from '../routes/Tools/pdf/mindmapInternalRoutes.js';
import financialAnalysisRoutes from '../routes/Tools/pdf/financialAnalysisRoutes.js';

const router = express.Router();

// Mount PDF chat routes
router.use('/chat', pdfChatRoutes);

// Mount PDF API routes
router.use('/pdf', pdfApiRoutes);

// Mount mindmap internal routes
router.use('/internal/mindmap', mindmapInternalRoutes);

// Mount financial analysis routes
router.use('/financial', financialAnalysisRoutes);

export default router;