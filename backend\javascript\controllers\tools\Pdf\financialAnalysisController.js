// backend/javascript/controllers/tools/Pdf/financialAnalysisController.js
import axios from 'axios';
import FormData from 'form-data';

// Python service URL for financial analysis
const PYTHON_FINANCIAL_ANALYSIS_URL = 
    process.env.PYTHON_FINANCIAL_ANALYSIS_URL || 
    'http://localhost:5001/api/financial/analyze-pdf';

/**
 * Controller for analyzing financial PDF documents
 * Forwards the PDF to Python service for processing and returns structured financial data
 */
export const analyzeFinancialPdf = async (req, res, next) => {
    try {
        // Check if file was uploaded
        if (!req.file) {
            return res.status(400).json({ 
                error: 'No PDF file uploaded. Please upload a financial PDF document.' 
            });
        }

        const originalFilename = req.file.originalname || 'financial_document.pdf';
        console.log(`[FinancialCtrl] Received PDF "${originalFilename}" for financial analysis`);

        // Prepare form data for Python service
        const formDataToPython = new FormData();
        formDataToPython.append('pdfFile', req.file.buffer, {
            filename: originalFilename,
            contentType: req.file.mimetype || 'application/pdf',
        });

        // Add any additional parameters
        const analysisType = req.body.analysisType || 'comprehensive';
        formDataToPython.append('analysisType', analysisType);

        console.log(`[FinancialCtrl] Forwarding PDF to Python financial analysis service...`);

        // Forward to Python service
        const pythonResponse = await axios.post(PYTHON_FINANCIAL_ANALYSIS_URL, formDataToPython, {
            headers: {
                ...formDataToPython.getHeaders(),
                'Content-Type': 'multipart/form-data',
            },
            timeout: 120000, // 2 minutes timeout for complex financial analysis
        });

        console.log(`[FinancialCtrl] Python service responded successfully`);

        // Structure the response for frontend
        const analysisResults = {
            success: true,
            originalFilename,
            analysisType,
            timestamp: new Date().toISOString(),
            data: pythonResponse.data,
            source: 'financial_analysis_service'
        };

        return res.status(200).json(analysisResults);

    } catch (error) {
        console.error('[FinancialCtrl] Error during financial analysis:', error);

        // Handle different types of errors
        if (error.code === 'ECONNREFUSED') {
            return res.status(503).json({
                error: 'Financial analysis service is currently unavailable. Please try again later.',
                details: 'Python service connection failed'
            });
        }

        if (error.response) {
            // Python service returned an error
            const statusCode = error.response.status || 500;
            const errorMessage = error.response.data?.error || 
                               error.response.data?.message || 
                               'Financial analysis failed';
            
            console.error(`[FinancialCtrl] Python service error (${statusCode}):`, errorMessage);
            
            return res.status(statusCode).json({
                error: errorMessage,
                details: error.response.data?.details || 'Error from financial analysis service'
            });
        }

        if (error.code === 'ENOTFOUND') {
            return res.status(503).json({
                error: 'Financial analysis service is not reachable. Please check service configuration.',
                details: 'DNS resolution failed for Python service'
            });
        }

        if (error.code === 'ETIMEDOUT') {
            return res.status(408).json({
                error: 'Financial analysis is taking longer than expected. Please try with a smaller document.',
                details: 'Request timeout'
            });
        }

        // Generic error handling
        return res.status(500).json({
            error: 'An unexpected error occurred during financial analysis.',
            details: error.message || 'Unknown error',
            timestamp: new Date().toISOString()
        });
    }
};

/**
 * Controller for getting supported financial document types
 */
export const getSupportedDocumentTypes = async (req, res) => {
    try {
        const supportedTypes = {
            financial_statements: {
                name: 'Financial Statements',
                description: 'Balance sheets, income statements, cash flow statements',
                supported_formats: ['PDF'],
                key_extractions: [
                    'Revenue and expenses',
                    'Assets and liabilities', 
                    'Cash flow data',
                    'Financial ratios'
                ]
            },
            annual_reports: {
                name: 'Annual Reports',
                description: 'Company annual reports and 10-K filings',
                supported_formats: ['PDF'],
                key_extractions: [
                    'Executive summary',
                    'Financial highlights',
                    'Risk factors',
                    'Management discussion'
                ]
            },
            investment_reports: {
                name: 'Investment Reports',
                description: 'Portfolio reports, fund performance, investment analysis',
                supported_formats: ['PDF'],
                key_extractions: [
                    'Portfolio composition',
                    'Performance metrics',
                    'Risk analysis',
                    'Investment recommendations'
                ]
            },
            budget_reports: {
                name: 'Budget Reports',
                description: 'Budget planning, variance analysis, forecasting',
                supported_formats: ['PDF'],
                key_extractions: [
                    'Budget vs actual',
                    'Variance analysis',
                    'Forecasting data',
                    'Department breakdowns'
                ]
            }
        };

        return res.status(200).json({
            success: true,
            supported_types: supportedTypes,
            total_types: Object.keys(supportedTypes).length
        });

    } catch (error) {
        console.error('[FinancialCtrl] Error getting supported document types:', error);
        return res.status(500).json({
            error: 'Failed to retrieve supported document types',
            details: error.message
        });
    }
};

/**
 * Health check for financial analysis service
 */
export const healthCheck = async (req, res) => {
    try {
        // Ping the Python service
        const healthResponse = await axios.get(
            `${PYTHON_FINANCIAL_ANALYSIS_URL.replace('/analyze-pdf', '/health')}`,
            { timeout: 5000 }
        );

        return res.status(200).json({
            status: 'healthy',
            service: 'financial_analysis',
            python_service: healthResponse.data,
            timestamp: new Date().toISOString()
        });

    } catch (error) {
        console.error('[FinancialCtrl] Health check failed:', error);
        return res.status(503).json({
            status: 'unhealthy',
            service: 'financial_analysis',
            error: error.message,
            timestamp: new Date().toISOString()
        });
    }
};
