// backend/javascript/routes/Tools/pdf/financialAnalysisRoutes.js
import express from 'express';
import multer from 'multer';
import { 
    analyzeFinancialPdf, 
    getSupportedDocumentTypes,
    healthCheck 
} from '../../../controllers/tools/Pdf/financialAnalysisController.js';
import { protect } from '../../../middleware/authMiddleware.js';

const router = express.Router();

// Configure multer for memory storage
const memoryStorage = multer.memoryStorage();
const upload = multer({ 
    storage: memoryStorage,
    limits: { fileSize: 25 * 1024 * 1024 }, // 25MB limit
    fileFilter: (req, file, cb) => {
        if (file.mimetype === 'application/pdf') {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type: Only PDF files are allowed for financial analysis.'), false);
        }
    }
});

/**
 * @route POST /api/pdf/financial/analyze
 * @desc Analyze a financial PDF document
 * @access Private
 */
router.post(
    '/analyze',
    protect,
    upload.single('pdfFile'),
    analyzeFinancialPdf
);

/**
 * @route GET /api/pdf/financial/document-types
 * @desc Get supported financial document types
 * @access Public
 */
router.get(
    '/document-types',
    getSupportedDocumentTypes
);

/**
 * @route GET /api/pdf/financial/health
 * @desc Health check for financial analysis service
 * @access Public
 */
router.get(
    '/health',
    healthCheck
);

export default router;
