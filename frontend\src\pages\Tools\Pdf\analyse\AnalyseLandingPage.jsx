// src/pages/Tools/Pdf/analyse/AnalyseLandingPage.jsx
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiDollarSign, FiTrendingUp, FiBarChart, FiPieChart } from 'react-icons/fi';

const AnalyseLandingPage = () => {
  const navigate = useNavigate();

  const analysisTools = [
    {
      id: 'financial',
      title: 'Financial Analysis',
      description: 'Extract key financial data, tables, and metrics from financial documents including balance sheets, income statements, and financial reports.',
      icon: FiDollarSign,
      color: 'red',
      features: [
        'Financial table extraction',
        'Key metrics identification',
        'Revenue and expense analysis',
        'Balance sheet parsing'
      ],
      path: '/app/pdf/analyse/financial'
    },
    {
      id: 'market',
      title: 'Market Analysis',
      description: 'Analyze market research documents, competitor analysis, and industry reports to extract valuable business insights.',
      icon: FiTrendingUp,
      color: 'blue',
      features: [
        'Market trend analysis',
        'Competitor insights',
        'Industry benchmarks',
        'Growth projections'
      ],
      path: '/app/pdf/analyse/market',
      comingSoon: true
    },
    {
      id: 'performance',
      title: 'Performance Reports',
      description: 'Process performance reports, KPI dashboards, and operational metrics to understand business performance.',
      icon: FiBarChart,
      color: 'sky',
      features: [
        'KPI extraction',
        'Performance metrics',
        'Operational insights',
        'Trend analysis'
      ],
      path: '/app/pdf/analyse/performance',
      comingSoon: true
    },
    {
      id: 'investment',
      title: 'Investment Analysis',
      description: 'Analyze investment documents, portfolio reports, and due diligence materials for investment insights.',
      icon: FiPieChart,
      color: 'blue',
      features: [
        'Portfolio analysis',
        'Risk assessment',
        'Return calculations',
        'Investment metrics'
      ],
      path: '/app/pdf/analyse/investment',
      comingSoon: true
    }
  ];

  const handleCardClick = (tool) => {
    if (tool.comingSoon) {
      // You can add a coming soon modal here if needed
      return;
    }
    navigate(tool.path);
  };

  const getColorClasses = (color, isComingSoon = false) => {
    const opacity = isComingSoon ? '50' : '100';
    const baseClasses = {
      red: {
        border: `border-red-600/${opacity}`,
        bg: `bg-gray-900/${opacity}`,
        iconBg: `bg-red-900/${opacity === '100' ? '70' : '40'}`,
        iconColor: `text-red-500/${opacity}`,
        shadow: 'shadow-red-500/40',
        gradient: 'from-gray-900 to-red-900/30',
        text: 'text-white',
        subtext: 'text-gray-300'
      },
      blue: {
        border: `border-blue-600/${opacity}`,
        bg: `bg-gray-900/${opacity}`,
        iconBg: `bg-blue-900/${opacity === '100' ? '70' : '40'}`,
        iconColor: `text-blue-500/${opacity}`,
        shadow: 'shadow-blue-500/40',
        gradient: 'from-gray-900 to-blue-900/30',
        text: 'text-white',
        subtext: 'text-gray-300'
      },
      sky: {
        border: `border-sky-600/${opacity}`,
        bg: `bg-gray-900/${opacity}`,
        iconBg: `bg-sky-900/${opacity === '100' ? '70' : '40'}`,
        iconColor: `text-sky-500/${opacity}`,
        shadow: 'shadow-sky-500/40',
        gradient: 'from-gray-900 to-sky-900/30',
        text: 'text-white',
        subtext: 'text-gray-300'
      }
    };
    return baseClasses[color];
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">
            Document Analysis Tools
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose from our specialized analysis tools to extract valuable insights from your PDF documents.
            Each tool is designed for specific document types and analysis needs.
          </p>
        </div>

      {/* Analysis Tools Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {analysisTools.map((tool) => {
          const colorClasses = getColorClasses(tool.color, tool.comingSoon);
          const IconComponent = tool.icon;
          
          return (
            <div
              key={tool.id}
              onClick={() => handleCardClick(tool)}
              className={`
                relative group cursor-pointer
                bg-gradient-to-br ${colorClasses.gradient} rounded-2xl border-2 ${colorClasses.border}
                ${colorClasses.shadow} hover:shadow-xl
                transition-all duration-300 ease-out
                hover:scale-[1.02] hover:-translate-y-1
                ${tool.comingSoon ? 'opacity-60' : 'hover:shadow-2xl'}
                overflow-hidden backdrop-blur-sm
              `}
            >
              {/* Coming Soon Badge */}
              {tool.comingSoon && (
                <div className="absolute top-4 right-4 z-10">
                  <span className="bg-gray-500 text-white text-xs font-medium px-3 py-1 rounded-full">
                    Coming Soon
                  </span>
                </div>
              )}

              <div className="p-8">
                {/* Icon and Title */}
                <div className="flex items-start mb-6">
                  <div className={`
                    flex-shrink-0 w-16 h-16 rounded-2xl ${colorClasses.iconBg} 
                    flex items-center justify-center mr-4
                    group-hover:scale-110 transition-transform duration-300
                  `}>
                    <IconComponent className={`w-8 h-8 ${colorClasses.iconColor}`} />
                  </div>
                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-white mb-2">
                      {tool.title}
                    </h3>
                    <p className="text-gray-300 leading-relaxed">
                      {tool.description}
                    </p>
                  </div>
                </div>

                {/* Features List */}
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-400 uppercase tracking-wide">
                    Key Features
                  </h4>
                  <ul className="space-y-2">
                    {tool.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-gray-300">
                        <div className={`
                          w-2 h-2 rounded-full ${colorClasses.iconColor.replace('text-', 'bg-')} mr-3 flex-shrink-0
                        `} />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Indicator */}
                <div className="mt-6 pt-4 border-t border-gray-700/50">
                  <div className="flex items-center justify-between">
                    <span className={`
                      text-sm font-medium ${tool.comingSoon ? 'text-gray-500' : colorClasses.iconColor}
                    `}>
                      {tool.comingSoon ? 'Available Soon' : 'Click to Start Analysis'}
                    </span>
                    {!tool.comingSoon && (
                      <div className={`
                        w-8 h-8 rounded-full ${colorClasses.iconBg}
                        flex items-center justify-center
                        group-hover:translate-x-1 transition-transform duration-300
                        backdrop-blur-sm
                      `}>
                        <svg
                          className={`w-4 h-4 ${colorClasses.iconColor}`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>

        {/* Bottom CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-gray-800/80 to-blue-900/40 rounded-2xl p-8 border border-blue-600/30 backdrop-blur-sm">
            <h2 className="text-2xl font-bold text-white mb-4">
              Need Help Choosing?
            </h2>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Not sure which analysis tool is right for your document? Our AI can help identify
              the best approach based on your document type and analysis goals.
            </p>
            <button className="bg-blue-600 hover:bg-blue-700 text-white font-medium px-6 py-3 rounded-lg transition-colors duration-200 shadow-lg hover:shadow-blue-500/25">
              Get Recommendations
            </button>
          </div>
        </div>
    </div>
    </div>
  );
};

export default AnalyseLandingPage;
