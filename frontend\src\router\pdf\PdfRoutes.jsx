// src/components/router/pdf/PdfRoutes.jsx
import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Import PDF related page components
import PdfSummaryUploadPage from '../../pages/Tools/Pdf/summaray/UploadPage';
import AnalysisPage from '../../pages/Tools/Pdf/summaray/SummarayPage';
import MindMapUploadPage from '../../pages/Tools/Pdf/mindmap/MindMapUploadPage';
// REMOVED: MindMapDisplayPage is no longer loaded or routed here.
import PdfChatUploadPage from '../../pages/Tools/Pdf/chat/PdfChatUploadPage';
import PdfChatSessionPage from '../../pages/Tools/Pdf/chat/PdfChatSessionPage';
import SavedItemsPage from '../../pages/Tools/Pdf/save/SavedItemsPage';
import AnalyseLandingPage from '../../pages/Tools/Pdf/analyse/AnalyseLandingPage';
import FinancialAnalysisPage from '../../pages/Tools/Pdf/analyse/Financial/FinancialAnalysisPage';

// Common UI Imports
import AnimatedPageWrapper from '../../common/Animations/AnimatedPageWrapper';
import ProtectedRoute from '../../components/common/Auth/ProtectedRoute';
import LayoutManager from '../../common/Layout/LayoutManager';

const PdfRoutes = ({ openLoginModal, openAuthPromptModal, openComingSoonModal }) => {
  const pdfSpecificRoutes = [
    { path: 'summary', PageElement: PdfSummaryUploadPage, isProtected: false },
    { path: 'analyse/:id', PageElement: AnalysisPage, isProtected: true },
    { path: 'analyse', PageElement: AnalyseLandingPage, isProtected: false },
    { path: 'analyse/financial', PageElement: FinancialAnalysisPage, isProtected: false },
    { path: 'mindmap', PageElement: MindMapUploadPage, isProtected: false },
    // REMOVED: The route for 'mindmap/display/:id' is gone from this array.
    { path: 'chat', PageElement: PdfChatUploadPage, isProtected: true },
    { path: 'chat/:sessionId', PageElement: PdfChatSessionPage, isProtected: true },
    { path: 'saved', PageElement: SavedItemsPage, isProtected: true },
  ];

  // The structure is now simpler as we don't need to handle a special full-screen case here.
  return (
    <LayoutManager
        openLoginModal={openLoginModal}
        openAuthPromptModal={openAuthPromptModal}
        openComingSoonModal={openComingSoonModal}
        showSidebar={true}
    >
      <Routes>
        <Route index element={<Navigate to="summary" replace />} />

        {pdfSpecificRoutes.map(({ path, PageElement, isProtected }) => {
          let pageContent = (
            <AnimatedPageWrapper>
              <PageElement
                openLoginModal={openLoginModal}
                openAuthPromptModal={openAuthPromptModal}
                openComingSoonModal={openComingSoonModal}
              />
            </AnimatedPageWrapper>
          );

          if (isProtected) {
            pageContent = <ProtectedRoute>{pageContent}</ProtectedRoute>;
          }

          return (
            <Route
              key={path}
              path={path}
              element={pageContent}
            />
          );
        })}
        <Route path="*" element={<Navigate to="summary" replace />} />
      </Routes>
    </LayoutManager>
  );
};

export default PdfRoutes; 