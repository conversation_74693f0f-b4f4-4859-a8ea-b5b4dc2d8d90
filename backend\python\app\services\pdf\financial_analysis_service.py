# backend/python/app/services/pdf/financial_analysis_service.py
import logging
import re
import json
from flask import current_app
import google.generativeai as genai
from tabulate import tabulate

logger = logging.getLogger(__name__)

class AIGatewayException(Exception):
    """Exception raised for errors in the AI gateway."""
    pass

def extract_tables_from_text(text):
    """
    Extract tables from text using pattern recognition
    """
    logger.info("Extracting tables from financial document text")
    
    # Simple pattern for financial tables (can be enhanced)
    table_patterns = [
        # Balance sheet pattern
        r'(?:BALANCE\s+SHEET|STATEMENT\s+OF\s+FINANCIAL\s+POSITION).*?(?=\n\s*\n|\Z)',
        # Income statement pattern
        r'(?:INCOME\s+STATEMENT|STATEMENT\s+OF\s+(?:COMPREHENSIVE\s+)?INCOME|PROFIT\s+AND\s+LOSS).*?(?=\n\s*\n|\Z)',
        # Cash flow pattern
        r'(?:CASH\s+FLOW\s+STATEMENT|STATEMENT\s+OF\s+CASH\s+FLOWS).*?(?=\n\s*\n|\Z)',
        # Financial ratios pattern
        r'(?:FINANCIAL\s+RATIOS|KEY\s+RATIOS|PERFORMANCE\s+INDICATORS).*?(?=\n\s*\n|\Z)'
    ]
    
    extracted_tables = []
    
    for pattern in table_patterns:
        matches = re.finditer(pattern, text, re.IGNORECASE | re.DOTALL)
        for match in matches:
            table_text = match.group(0)
            if len(table_text.strip().split('\n')) > 3:  # Ensure it's actually a table with multiple rows
                extracted_tables.append({
                    'table_text': table_text,
                    'table_type': identify_table_type(table_text)
                })
    
    logger.info(f"Extracted {len(extracted_tables)} potential tables from document")
    return extracted_tables

def identify_table_type(table_text):
    """
    Identify the type of financial table
    """
    table_text_lower = table_text.lower()
    
    if re.search(r'balance\s+sheet|financial\s+position|assets|liabilities|equity', table_text_lower):
        return "Balance Sheet"
    elif re.search(r'income\s+statement|profit\s+and\s+loss|revenue|expenses|net\s+income', table_text_lower):
        return "Income Statement"
    elif re.search(r'cash\s+flow|operating\s+activities|investing\s+activities|financing\s+activities', table_text_lower):
        return "Cash Flow Statement"
    elif re.search(r'ratio|performance\s+indicator|financial\s+highlight', table_text_lower):
        return "Financial Ratios"
    else:
        return "Unknown Financial Table"

def extract_key_metrics(text):
    """
    Extract key financial metrics from text
    """
    logger.info("Extracting key financial metrics")
    
    metrics_patterns = {
        'revenue': r'(?:Total\s+)?Revenue[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?',
        'net_income': r'Net\s+(?:Income|Profit)[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?',
        'total_assets': r'Total\s+Assets[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?',
        'total_liabilities': r'Total\s+Liabilities[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?',
        'equity': r'(?:Total\s+)?(?:Shareholders[\'']?\s+)?Equity[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?',
        'eps': r'(?:Basic\s+)?EPS|Earnings\s+Per\s+Share[:\s]+[$€£]?[\d,.]+',
        'dividend': r'Dividend(?:\s+Per\s+Share)?[:\s]+[$€£]?[\d,.]+',
        'operating_income': r'Operating\s+(?:Income|Profit)[:\s]+[$€£]?[\d,.]+\s*(?:million|billion|m|b)?'
    }
    
    extracted_metrics = {}
    
    for metric_name, pattern in metrics_patterns.items():
        matches = re.finditer(pattern, text, re.IGNORECASE)
        for match in matches:
            metric_text = match.group(0)
            extracted_metrics[metric_name] = metric_text.strip()
            break  # Take only the first match for each metric
    
    logger.info(f"Extracted {len(extracted_metrics)} key metrics")
    return extracted_metrics

def analyze_financial_document(document_text, analysis_type="comprehensive"):
    """
    Analyze financial document and extract structured data
    """
    logger.info(f"Analyzing financial document with analysis type: {analysis_type}")
    
    # Extract tables and metrics using pattern matching
    extracted_tables = extract_tables_from_text(document_text)
    extracted_metrics = extract_key_metrics(document_text)
    
    # Use AI to enhance the analysis
    ai_analysis = perform_ai_analysis(document_text, analysis_type)
    
    # Combine all results
    analysis_results = {
        "tables": extracted_tables,
        "key_metrics": extracted_metrics,
        "ai_analysis": ai_analysis
    }
    
    return analysis_results

def perform_ai_analysis(document_text, analysis_type):
    """
    Use AI to analyze financial document text
    """
    logger.info(f"Performing AI analysis for financial document")
    
    api_key = current_app.config.get('GEMINI_API_KEY')
    if not api_key:
        raise ValueError("AI Service Error: API key not configured.")
    
    try:
        genai.configure(api_key=api_key)
    except Exception as e:
        raise AIGatewayException(f"AI Service Error: SDK configuration failed: {e}")

    model_name = current_app.config.get('GEMINI_SUMMARIZATION_MODEL', 'gemini-1.5-flash-latest')
    
    try:
        model = genai.GenerativeModel(model_name)
        
        # Build prompt based on analysis type
        if analysis_type == "comprehensive":
            analysis_prompt = build_comprehensive_analysis_prompt(document_text)
        elif analysis_type == "ratios_only":
            analysis_prompt = build_ratios_analysis_prompt(document_text)
        elif analysis_type == "cash_flow_focus":
            analysis_prompt = build_cash_flow_analysis_prompt(document_text)
        else:
            analysis_prompt = build_comprehensive_analysis_prompt(document_text)
        
        response = model.generate_content(analysis_prompt)
        analysis_text = response.text
        
        if not analysis_text or not analysis_text.strip():
            raise ValueError("AI Service Error: Financial analysis was empty.")
            
        logger.info(f"AI financial analysis completed successfully")
        
        # Try to parse JSON from the response
        try:
            # Extract JSON if it's wrapped in markdown code blocks
            json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', analysis_text)
            if json_match:
                json_str = json_match.group(1)
                return json.loads(json_str)
            
            # If not in code blocks, try to parse the whole response
            return json.loads(analysis_text)
        except json.JSONDecodeError:
            # If JSON parsing fails, return the raw text
            logger.warning("Could not parse AI response as JSON, returning raw text")
            return {"raw_analysis": analysis_text}
        
    except Exception as e:
        logger.error(f"Error in AI financial analysis: {e}")
        raise AIGatewayException(f"Financial analysis failed: {e}")

def build_comprehensive_analysis_prompt(document_text):
    """
    Build prompt for comprehensive financial analysis
    """
    return f"""
You are an expert financial analyst. Analyze the following financial document and extract key information.

Return a JSON object with the following structure:
{{
  "document_type": "The type of financial document (e.g., Annual Report, Financial Statement, etc.)",
  "financial_tables": [
    {{
      "table_type": "Type of table (e.g., Income Statement, Balance Sheet, etc.)",
      "table_data": {{
        // Key financial figures from the table
      }}
    }}
  ],
  "key_metrics": {{
    "revenue": "Total revenue figure with currency",
    "net_income": "Net income figure with currency",
    "total_assets": "Total assets figure with currency",
    "total_liabilities": "Total liabilities figure with currency",
    "equity": "Shareholders' equity figure with currency"
    // Other key metrics found in the document
  }},
  "financial_ratios": {{
    "current_ratio": "Current ratio value",
    "quick_ratio": "Quick ratio value",
    "debt_to_equity": "Debt-to-equity ratio value",
    "return_on_assets": "ROA value",
    "return_on_equity": "ROE value"
    // Other financial ratios that can be calculated
  }},
  "summary": "A brief summary of the financial position based on the document"
}}

Document content:
{document_text}

Return only the JSON object, no additional text.
"""

def build_ratios_analysis_prompt(document_text):
    """
    Build prompt focused on financial ratios analysis
    """
    return f"""
You are an expert financial analyst. Analyze the following financial document and extract financial ratios.

Return a JSON object with the following structure:
{{
  "financial_ratios": {{
    "liquidity_ratios": {{
      "current_ratio": "value",
      "quick_ratio": "value",
      "cash_ratio": "value"
    }},
    "profitability_ratios": {{
      "gross_margin": "value",
      "operating_margin": "value",
      "net_profit_margin": "value",
      "return_on_assets": "value",
      "return_on_equity": "value"
    }},
    "leverage_ratios": {{
      "debt_to_equity": "value",
      "debt_to_assets": "value",
      "interest_coverage": "value"
    }},
    "efficiency_ratios": {{
      "asset_turnover": "value",
      "inventory_turnover": "value",
      "receivables_turnover": "value"
    }}
  }},
  "ratio_analysis": "A brief analysis of what these ratios indicate about the company's financial health"
}}

Document content:
{document_text}

Return only the JSON object, no additional text.
"""

def build_cash_flow_analysis_prompt(document_text):
    """
    Build prompt focused on cash flow analysis
    """
    return f"""
You are an expert financial analyst. Analyze the following financial document with a focus on cash flow.

Return a JSON object with the following structure:
{{
  "cash_flow_analysis": {{
    "operating_activities": {{
      "net_cash_from_operations": "value",
      "key_components": [
        // List of key components affecting operating cash flow
      ]
    }},
    "investing_activities": {{
      "net_cash_from_investing": "value",
      "key_components": [
        // List of key components affecting investing cash flow
      ]
    }},
    "financing_activities": {{
      "net_cash_from_financing": "value",
      "key_components": [
        // List of key components affecting financing cash flow
      ]
    }},
    "free_cash_flow": "value",
    "cash_flow_trends": "Analysis of cash flow trends"
  }},
  "cash_flow_ratios": {{
    "operating_cash_flow_ratio": "value",
    "cash_flow_to_debt_ratio": "value",
    "cash_flow_coverage_ratio": "value"
  }},
  "summary": "A brief summary of the company's cash flow position"
}}

Document content:
{document_text}

Return only the JSON object, no additional text.
"""
