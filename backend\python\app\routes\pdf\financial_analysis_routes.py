# backend/python/app/routes/pdf/financial_analysis_routes.py
import os
import tempfile
import json
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from app.services.pdf.pdf_utils import extract_text_from_pdf
from app.services.pdf.financial_analysis_service import analyze_financial_document, AIGatewayException
from app.config import Config

financial_analysis_bp = Blueprint('financial_analysis_bp', __name__)

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

@financial_analysis_bp.route('/analyze-pdf', methods=['POST'])
def analyze_financial_pdf():
    """
    Analyze a financial PDF document and extract key financial data
    """
    current_app.logger.info("Received request for financial PDF analysis")
    
    # Check if file is present
    if 'pdfFile' not in request.files:
        return jsonify({"error": "No PDF file part in the request."}), 400
    
    file = request.files['pdfFile']
    analysis_type = request.form.get('analysisType', 'comprehensive')
    
    if not file or not file.filename:
        return jsonify({"error": "No file selected."}), 400
    
    if not allowed_file(file.filename):
        return jsonify({"error": "Invalid file type. Only PDF files are allowed."}), 400

    original_filename = secure_filename(file.filename)
    tmp_pdf_path = None
    
    try:
        current_app.logger.info(f"Processing financial PDF: {original_filename}")
        
        # Save uploaded file to temporary location
        with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp:
            file.seek(0)
            tmp.write(file.read())
            tmp_pdf_path = tmp.name

        # Extract text from PDF
        extracted_text = extract_text_from_pdf(tmp_pdf_path)
        if not extracted_text or not extracted_text.strip():
            return jsonify({"error": "Text extraction failed or PDF is empty."}), 400

        current_app.logger.info(f"Extracted {len(extracted_text)} characters from PDF")

        # Analyze financial content
        try:
            analysis_results = analyze_financial_document(extracted_text, analysis_type)
            
            # Structure the response
            response_data = {
                "success": True,
                "original_filename": original_filename,
                "analysis_type": analysis_type,
                "document_length": len(extracted_text),
                "analysis_results": analysis_results,
                "processing_info": {
                    "service": "financial_analysis",
                    "version": "1.0.0",
                    "timestamp": current_app.config.get('REQUEST_TIMESTAMP', 'unknown')
                }
            }
            
            current_app.logger.info(f"Financial analysis completed successfully for {original_filename}")
            return jsonify(response_data), 200
            
        except AIGatewayException as e:
            current_app.logger.error(f"AI Gateway error during financial analysis: {e}")
            return jsonify({
                "error": "AI service error during financial analysis",
                "details": str(e),
                "type": "ai_gateway_error"
            }), 503
            
        except ValueError as e:
            current_app.logger.error(f"Value error during financial analysis: {e}")
            return jsonify({
                "error": "Invalid data for financial analysis",
                "details": str(e),
                "type": "validation_error"
            }), 400
            
        except Exception as e:
            current_app.logger.error(f"Unexpected error during financial analysis: {e}")
            return jsonify({
                "error": "Financial analysis failed",
                "details": str(e),
                "type": "processing_error"
            }), 500

    except Exception as e:
        current_app.logger.error(f"Error processing financial PDF {original_filename}: {e}")
        return jsonify({
            "error": "Failed to process PDF file",
            "details": str(e),
            "type": "file_processing_error"
        }), 500
        
    finally:
        # Clean up temporary file
        if tmp_pdf_path and os.path.exists(tmp_pdf_path):
            try:
                os.unlink(tmp_pdf_path)
                current_app.logger.debug(f"Cleaned up temporary file: {tmp_pdf_path}")
            except Exception as cleanup_error:
                current_app.logger.warning(f"Failed to clean up temporary file {tmp_pdf_path}: {cleanup_error}")

@financial_analysis_bp.route('/health', methods=['GET'])
def health_check():
    """
    Health check endpoint for financial analysis service
    """
    try:
        # Basic health check
        health_status = {
            "status": "healthy",
            "service": "financial_analysis",
            "version": "1.0.0",
            "capabilities": [
                "financial_statement_analysis",
                "table_extraction",
                "key_metrics_identification",
                "ratio_calculation"
            ],
            "supported_formats": ["PDF"],
            "max_file_size_mb": 25
        }
        
        return jsonify(health_status), 200
        
    except Exception as e:
        current_app.logger.error(f"Health check failed: {e}")
        return jsonify({
            "status": "unhealthy",
            "service": "financial_analysis",
            "error": str(e)
        }), 503

@financial_analysis_bp.route('/supported-metrics', methods=['GET'])
def get_supported_metrics():
    """
    Get list of supported financial metrics and analysis types
    """
    try:
        supported_metrics = {
            "financial_ratios": [
                "Current Ratio",
                "Quick Ratio", 
                "Debt-to-Equity Ratio",
                "Return on Assets (ROA)",
                "Return on Equity (ROE)",
                "Gross Profit Margin",
                "Net Profit Margin",
                "Operating Margin"
            ],
            "key_financial_items": [
                "Total Revenue",
                "Net Income",
                "Total Assets",
                "Total Liabilities",
                "Shareholders' Equity",
                "Operating Cash Flow",
                "Free Cash Flow",
                "EBITDA"
            ],
            "analysis_types": [
                "comprehensive",
                "ratios_only",
                "cash_flow_focus",
                "profitability_analysis",
                "liquidity_analysis"
            ],
            "table_extraction": [
                "Income Statement",
                "Balance Sheet", 
                "Cash Flow Statement",
                "Financial Highlights",
                "Key Performance Indicators"
            ]
        }
        
        return jsonify({
            "success": True,
            "supported_metrics": supported_metrics
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting supported metrics: {e}")
        return jsonify({
            "error": "Failed to retrieve supported metrics",
            "details": str(e)
        }), 500
